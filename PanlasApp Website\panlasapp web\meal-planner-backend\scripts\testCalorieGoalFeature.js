const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

const baseURL = 'http://localhost:5000/api';

async function testCalorieGoalFeature() {
  console.log('🧪 Testing Calorie Goal Feature...\n');

  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Test user credentials (you may need to adjust these)
    const testUser = {
      email: '<EMAIL>',
      password: 'testpassword123'
    };

    // 1. Login to get auth token
    console.log('\n1. Logging in...');
    let authToken;
    try {
      const loginResponse = await axios.post(`${baseURL}/auth/login`, testUser);
      authToken = loginResponse.data.token;
      console.log('✅ Login successful');
    } catch (error) {
      console.log('⚠️ Login failed, creating test user...');
      try {
        await axios.post(`${baseURL}/auth/register`, {
          ...testUser,
          username: 'testuser',
          firstName: 'Test',
          lastName: 'User'
        });
        const loginResponse = await axios.post(`${baseURL}/auth/login`, testUser);
        authToken = loginResponse.data.token;
        console.log('✅ Test user created and logged in');
      } catch (regError) {
        console.error('❌ Failed to create test user:', regError.response?.data || regError.message);
        return;
      }
    }

    const headers = { Authorization: `Bearer ${authToken}` };

    // 2. Test getting goals (should include new calorie goal)
    console.log('\n2. Testing GET /ai/goals...');
    try {
      const goalsResponse = await axios.get(`${baseURL}/ai/goals`, { headers });
      console.log('✅ Goals endpoint successful');
      
      const calorieGoal = goalsResponse.data.goals.find(goal => goal.id === 'set_calorie_goal');
      if (calorieGoal) {
        console.log('✅ Calorie goal option found:', calorieGoal.name);
      } else {
        console.log('❌ Calorie goal option not found in goals list');
      }
    } catch (error) {
      console.error('❌ Goals endpoint failed:', error.response?.data || error.message);
    }

    // 3. Test setting calorie goal
    console.log('\n3. Testing POST /ai/set-calorie-goal...');
    try {
      const calorieGoalResponse = await axios.post(`${baseURL}/ai/set-calorie-goal`, {
        calorieGoal: 2000
      }, { headers });
      
      console.log('✅ Set calorie goal endpoint successful');
      console.log('Calorie goal set to:', calorieGoalResponse.data.calorieGoal);
      console.log('Recommendations count:', calorieGoalResponse.data.recommendations?.length || 0);
      console.log('Personalized message:', calorieGoalResponse.data.personalizedMessage?.substring(0, 100) + '...');
    } catch (error) {
      console.error('❌ Set calorie goal endpoint failed:', error.response?.data || error.message);
    }

    // 4. Test with invalid calorie goal
    console.log('\n4. Testing with invalid calorie goal...');
    try {
      await axios.post(`${baseURL}/ai/set-calorie-goal`, {
        calorieGoal: 500 // Too low
      }, { headers });
      console.log('❌ Should have failed with invalid calorie goal');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Correctly rejected invalid calorie goal');
      } else {
        console.error('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    console.log('\n🎉 Calorie goal feature testing completed!');

  } catch (error) {
    console.error('❌ Test setup failed:', error);
  }
}

testCalorieGoalFeature();
